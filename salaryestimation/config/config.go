//go:generate conf_gen github.com/epifi/gamma/salaryestimation/config Config
package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"time"

	"github.com/epifi/be-common/pkg/cfg"
	sdkconfig "github.com/epifi/be-common/quest/sdk/config"
)

var (
	once   sync.Once
	config *Config
	err    error
)

var (
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})
	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(configDirPath, cfg.SALARY_ESTIMATION_SERVICE)
	if err != nil {
		return nil, fmt.Errorf("failed to load config %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	updateDefaultConfig(conf)
	return conf, nil
}

// update the env variable
func updateDefaultConfig(c *Config) {
	readAndSetEnv(c)
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

type Config struct {
	Application                  *Application
	Logging                      *cfg.Logging
	SecureLogging                *cfg.SecureLogging
	Flags                        *Flags            `dynamic:"true"`
	QuestSdk                     *sdkconfig.Config `dynamic:"true"`
	OldestAATransactionUpdatedAt time.Time
}

type Application struct {
	Environment string
	Name        string
	ServerName  string
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool `dynamic:"true"`
}
