package impl

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/structpb"
	"gorm.io/gorm"

	pkgTestV2 "github.com/epifi/be-common/pkg/test/v2"
	"github.com/epifi/gamma/api/salaryestimation"
	seTypes "github.com/epifi/gamma/api/typesv2/salaryestimation"
	"github.com/epifi/gamma/salaryestimation/dao"
)

var (
	affectedTestTables = []string{"salary_estimation_attempts"}
)

type salaryEstimationAttemptDaoTestSuite struct {
	dao    dao.SalaryEstimationAttemptDao
	dbName string
	db     *gorm.DB
}

func TestSalaryEstimationAttemptDao_Create(t *testing.T) {
	t.Parallel()

	ts := setupTestSuite(t)

	testCases := []struct {
		name        string
		attempt     *salaryestimation.SalaryEstimationAttempt
		expectError bool
	}{
		{
			name: "successful creation",
			attempt: &salaryestimation.SalaryEstimationAttempt{
				ClientReqId: "test-client-req-id-1",
				Source:      seTypes.Source_SOURCE_AA,
				ActorId:     "test-actor-id-1",
				Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_INITIATED,
			},
			expectError: false,
		},
		{
			name: "creation with minimal fields",
			attempt: &salaryestimation.SalaryEstimationAttempt{
				ClientReqId: "test-client-req-id-2",
				ActorId:     "test-actor-id-2",
			},
			expectError: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Clean database before each test
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, ts.db, ts.dbName, affectedTestTables)

			result, err := ts.dao.Create(context.Background(), tc.attempt)

			if tc.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.NotEmpty(t, result.GetId())
				assert.Equal(t, tc.attempt.GetClientReqId(), result.GetClientReqId())
				assert.Equal(t, tc.attempt.GetActorId(), result.GetActorId())
				assert.NotNil(t, result.GetCreatedAt())
				assert.NotNil(t, result.GetUpdatedAt())
			}
		})
	}
}

func TestSalaryEstimationAttemptDao_GetByID(t *testing.T) {
	t.Parallel()

	ts := setupTestSuite(t)

	// Clean database
	pkgTestV2.TruncateAndPopulateRdsFixtures(t, ts.db, ts.dbName, affectedTestTables)

	// Create a test record
	attempt := &salaryestimation.SalaryEstimationAttempt{
		ClientReqId: "test-client-req-id",
		Source:      seTypes.Source_SOURCE_AA,
		ActorId:     "test-actor-id",
		Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_INITIATED,
	}

	created, err := ts.dao.Create(context.Background(), attempt)
	require.NoError(t, err)
	require.NotNil(t, created)

	// Test GetByID
	result, err := ts.dao.GetByID(context.Background(), created.GetId())
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, created.GetId(), result.GetId())
	assert.Equal(t, created.GetClientReqId(), result.GetClientReqId())
	assert.Equal(t, created.GetActorId(), result.GetActorId())

	// Test GetByID with non-existent ID
	_, err = ts.dao.GetByID(context.Background(), "non-existent-id")
	assert.Error(t, err)
}

func TestSalaryEstimationAttemptDao_GetByClientReqID(t *testing.T) {
	t.Parallel()

	ts := setupTestSuite(t)

	// Clean database
	pkgTestV2.TruncateAndPopulateRdsFixtures(t, ts.db, ts.dbName, affectedTestTables)

	// Create a test record
	attempt := &salaryestimation.SalaryEstimationAttempt{
		ClientReqId: "unique-client-req-id",
		Source:      seTypes.Source_SOURCE_AA,
		ActorId:     "test-actor-id",
		Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_INITIATED,
	}

	created, err := ts.dao.Create(context.Background(), attempt)
	require.NoError(t, err)
	require.NotNil(t, created)

	// Test GetByClientReqID
	result, err := ts.dao.GetByClientReqID(context.Background(), created.GetClientReqId())
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, created.GetId(), result.GetId())
	assert.Equal(t, created.GetClientReqId(), result.GetClientReqId())

	// Test GetByClientReqID with non-existent client req ID
	_, err = ts.dao.GetByClientReqID(context.Background(), "non-existent-client-req-id")
	assert.Error(t, err)
}
