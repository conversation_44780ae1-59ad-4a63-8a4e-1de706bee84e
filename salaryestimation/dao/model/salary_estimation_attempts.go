package model

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/gamma/api/salaryestimation"
	seTypes "github.com/epifi/gamma/api/typesv2/salaryestimation"
)

// SalaryEstimationAttempt represents the database model for salary estimation attempts
type SalaryEstimationAttempt struct {
	ID          string `gorm:"primary_key;type:uuid;default:gen_random_uuid()"`
	ClientReqID string
	Source      seTypes.Source
	ActorID     string
	Status      salaryestimation.AttemptStatus
	CreatedAt   time.Time
	UpdatedAt   time.Time
	DeletedAt   gorm.DeletedAt
}

// NewSalaryEstimationAttempt creates a new model from proto
func NewSalaryEstimationAttempt(proto *salaryestimation.SalaryEstimationAttempt) *SalaryEstimationAttempt {
	model := &SalaryEstimationAttempt{
		ID:          proto.GetId(),
		ClientReqID: proto.GetClientReqId(),
		Source:      proto.GetSource(),
		ActorID:     proto.GetActorId(),
		Status:      proto.GetStatus(),
	}
	if proto.GetCreatedAt() != nil {
		model.CreatedAt = proto.GetCreatedAt().AsTime()
	}
	if proto.GetUpdatedAt() != nil {
		model.UpdatedAt = proto.GetUpdatedAt().AsTime()
	}
	if proto.GetDeletedAt() != nil {
		model.DeletedAt = gorm.DeletedAt{
			Time:  proto.GetDeletedAt().AsTime(),
			Valid: true,
		}
	}
	return model
}

// GetProto converts model to proto
func (m *SalaryEstimationAttempt) GetProto() *salaryestimation.SalaryEstimationAttempt {
	proto := &salaryestimation.SalaryEstimationAttempt{
		Id:          m.ID,
		ClientReqId: m.ClientReqID,
		Source:      m.Source,
		ActorId:     m.ActorID,
		Status:      m.Status,
		CreatedAt:   timestamppb.New(m.CreatedAt),
		UpdatedAt:   timestamppb.New(m.UpdatedAt),
	}
	if m.DeletedAt.Valid {
		proto.DeletedAt = timestamppb.New(m.DeletedAt.Time)
	}
	return proto
}
